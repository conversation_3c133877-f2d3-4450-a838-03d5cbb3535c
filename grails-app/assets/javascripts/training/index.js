function updateDoc(id) {
    $.get(tractionWebRoot + '/training/updateDoc?id=' + id, function(data) {
        $('#doc').html(data);
    });
}

function updateOrdreSelect(selector, nbSection) {
    $(selector).empty();
    for (i = 1; i <= nbSection; i++) {
        if (i < nbSection) {
            $(selector).append('<option value="' + i + '">' + i + '</option>');
        } else {
            $(selector).append('<option selected value="' + i + '">' + i + '</option>');
        }
    }
}
// Sélection par radio => met à jour la classe active + appel serveur
document.querySelectorAll('#langList input[type=radio]').forEach(r => {
    r.addEventListener('change', (e) => {
        document.querySelectorAll('#langList .lang-item').forEach(li => li.classList.remove('active'));
        e.target.closest('.lang-item').classList.add('active');

        fetch('/training/setDefaultLanguage', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ code: e.target.value })
        }).catch(() => {});
    });
});

// Suppression d’une langue
document.querySelectorAll('.delete-lang').forEach(btn => {
    btn.addEventListener('click', () => {
        const code = btn.dataset.code;
        if (!confirm('Supprimer cette langue ?')) return;

        fetch('/training/deleteLanguage', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ code })
        }).then(() => location.reload());
    });
});

// Ajouter une langue (ouvre ton modal existant)
const addBtn = document.getElementById('addLanguageBtn');
if (addBtn) {
    addBtn.addEventListener('click', () => {
        TractionModal?.show
            ? TractionModal.show({ url: '/training/modallanguage' })
            : (window.location.href = '/training/modallanguage');
    });
}
