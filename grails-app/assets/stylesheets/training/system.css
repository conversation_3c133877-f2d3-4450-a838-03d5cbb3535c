.calendar-list {
    list-style: none;
    padding-left: 1em;
}
.calendar-list i {
    vertical-align: middle;
}

#soldboard-colors {
    list-style: none;
    padding-left: 15px;
}
#soldboard-colors span {
    padding: 5px;
    font-weight: bold;
    border: 1px solid lightgray;
}
#soldboard-colors li {
    margin-bottom: 10px;
}

/* ===== Arrière-plan global + espacements ===== */
body { background:#f6f8fb; }
.tab-content.m-4 { margin:1.25rem !important; }

/* ===== Panneau "LÉGENDE DU TYPE DE CARTES" ===== */
#tabCardType .card {
    border:1px solid #e6e9ef;
    border-radius:12px;
    box-shadow:0 1px 2px rgba(16,24,40,.04);
}
#tabCardType .card-header {
    background:#f6f8fb;
    border-bottom:1px solid #e6e9ef;
    padding:.6rem .85rem;
}
#tabCardType .card-header .small {
    font-weight:700;
    letter-spacing:.4px;
    color:#344054;
}

/* ===== Accordéons généraux ===== */
#tabCardType .accordion-button {
    background:#f8f9fa !important;
    border:1px solid #dfe3ea !important;
    box-shadow:none !important;
    padding:.55rem .8rem;
    color:#000 !important;       /* noir */
    font-weight:400 !important;  /* normal, pas bold */
    margin:0;
}
#tabCardType .accordion-item { border:0; }

/* === TRADE PS === */
#headingTrade > .accordion-button {
    border-radius:8px 8px 0 0 !important; /* arrondi haut */
    border-bottom:none !important;
    margin:0;
}
#collapseTrade.accordion-collapse > .accordion-body,
#tabCardType #collapseTrade > .accordion-body {
    background:#f3f5f9 !important;   /* gris clair */
    border:1px solid #e1e6ef;
    border-top:none !important;
    border-radius:0 0 8px 8px;       /* arrondi bas */
    padding:.6rem;
    margin:0;
}

/* === BATEAUX VENDUS === */
#headingBoats > .accordion-button {
    margin:0 !important;             /* aligné à gauche */
    border-radius:8px 8px 0 0 !important;
    border-bottom:none !important;
    background:#f8f9fa !important;
    border:1px solid #e6e9ef !important;
}
#collapseBoats.accordion-collapse > .accordion-body,
#tabCardType #collapseBoats > .accordion-body {
    margin:0 !important;
    background:#ffffff !important;
    border:1px solid #e6e9ef;
    border-top:none !important;
    border-radius:0 0 8px 8px;
    padding:.6rem;
}

/* ===== Flèche bleue plus grasse ===== */
#headingTrade .accordion-button::after,
#headingBoats .accordion-button::after,
#headingPowersport .accordion-button::after,
#headingEntreposage .accordion-button::after { display:none; }
#headingTrade .accordion-button::before,
#headingBoats .accordion-button::before,
#headingPowersport .accordion-button::before,
#headingEntreposage .accordion-button::before {
    content:"";
    width:16px; height:16px;
    margin-right:.5rem;
    background-repeat:no-repeat;
    background-position:center;
    background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath d='M2 5l6 6 6-6' stroke='%230d6efd' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'/%3e%3c/svg%3e");
    transform:rotate(-90deg);
    transition:transform .2s ease;
}
#headingTrade .accordion-button:not(.collapsed)::before,
#headingBoats .accordion-button:not(.collapsed)::before,
#headingPowersport .accordion-button:not(.collapsed)::before,
#headingEntreposage .accordion-button:not(.collapsed)::before {
    transform:rotate(0deg);
}

/* ====== Organisation FLEX (5 cartes minimum par ligne) ====== */
#collapseBoats .legend-flex{
    --gap: 12px;
    display:flex;
    flex-wrap:wrap;
    gap:var(--gap);
    align-items:stretch;
}
#collapseBoats .legend-item{
    flex:0 0 calc((100% - (4 * var(--gap))) / 5);
    max-width:calc((100% - (4 * var(--gap))) / 5);
    box-sizing:border-box;
}
@media (max-width: 1400px){
    #collapseBoats .legend-item{
        flex:0 0 calc((100% - (3 * var(--gap))) / 4);
        max-width:calc((100% - (3 * var(--gap))) / 4);
    }
}
@media (max-width: 1100px){
    #collapseBoats .legend-item{
        flex:0 0 calc((100% - (2 * var(--gap))) / 3);
        max-width:calc((100% - (2 * var(--gap))) / 3);
    }
}
@media (max-width: 820px){
    #collapseBoats .legend-item{
        flex:0 0 calc((100% - var(--gap)) / 2);
        max-width:calc((100% - var(--gap)) / 2);
    }
}
@media (max-width: 560px){
    #collapseBoats .legend-item{
        flex:0 0 100%;
        max-width:100%;
    }
}
#collapseBoats .legend-item .card{
    width:100%;
    height:100%;
    border:1px solid #e6e9ef;
    border-radius:12px;
    box-shadow:0 1px 2px rgba(16,24,40,.06);
}

/* ===== Styles cartes ===== */
#collapseBoats .card-header {
    background:#fff;
    border-bottom:1px solid #eef1f5;
    padding:.45rem .6rem;
}
#collapseBoats .card-header h6 {
    margin:0;
    font-weight:800;
    font-size:.8rem;
    color:#0f172a;
}
#collapseBoats .card-header .badge {
    width:14px; height:14px;
    border-radius:999px;
    background:#0ea5e9;
    color:transparent;
    line-height:14px;
    padding:0;
    display:inline-block;
    box-shadow:inset 0 0 0 2px #e6f3fb;
}
#collapseBoats .card-body{ padding:.5rem; }
#collapseBoats traction\:card {
    display:block;
    border:1px solid #dfe3ea;
    border-radius:10px;
    overflow:hidden;
    min-height:160px;
    box-shadow:inset 0 0 0 1px rgba(16,24,40,.02);
}

/* ===== Listes des autres onglets ===== */
.tab-pane .list-group-item {
    border:1px solid #e6e9ef;
    border-radius:12px;
    margin:.4rem 0;
    padding:.55rem .8rem;
}

/* ===== Tableaux ===== */
.table {
    background:#fff;
    border:1px solid #e6e9ef;
    border-radius:12px;
    overflow:hidden;
}
.table thead th {
    background:#f3f6fb !important;
    color:#0f172a;
    font-weight:700;
    border-bottom:1px solid #e6e9ef !important;
}
.table td, .table th { border-color:#eef1f5 !important; }

/* ===== Soldboard: puces de couleur arrondies ===== */
#soldboard-colors .badge.rounded-pill {
    display:inline-block;
    min-width:28px;
    height:16px;
    padding:0;
    border-radius:999px;
}

/* ===== Badges statut véhicule ===== */
#vehicleStatus .badge.rounded-pill {
    font-weight:700;
    display:inline-flex;
    align-items:center;
    gap:.35rem;
    padding:.35rem .6rem;
}

/* --------- SKIN corrigé pour les cartes --------- */
.tc-skin{
    /* variables faciles à retoucher */
    --skin-pad-x: 12px;      /* padding droit/gauche (hors barre) */
    --skin-pad-y: 12px;      /* padding haut/bas */
    --bar-left:   16px;      /* position de la barre depuis le bord gauche */
    --bar-w:      6px;       /* largeur de la barre orange */
    --bar-gap:    20px;      /* espace entre la barre et le contenu réel */

    position: relative;
    min-height: 155px;
    /* on réserve la place: pad gauche = bar-left + bar-w + bar-gap */
    padding: var(--skin-pad-y) var(--skin-pad-x) var(--skin-pad-y)
    calc(var(--bar-left) + var(--bar-w) + var(--bar-gap));
    background: #e6e7ea;                 /* gris clair */
    border: 1px solid #cfd3da;
    border-radius: 10px;
    box-shadow: 0 1px 2px rgba(0,0,0,.08);
    overflow: hidden;
}

/* Barre orange à gauche */
.tc-skin::before{
    content:"";
    position:absolute;
    top:  var(--skin-pad-y);
    bottom: var(--skin-pad-y);
    left: var(--bar-left);
    width: var(--bar-w);
    background:#ff8a00;                   /* orange */
    border-radius: 3px;
}

/* Pastille verte centrée sur la barre */
.tc-skin::after{
    content:"";
    position:absolute;
    left: calc(var(--bar-left) - 6px);     /* centré par rapport à la barre */
    top: 50%;
    transform: translateY(-50%);
    width:14px; height:14px;
    background:#19c37d;
    border:2px solid #fff;
    border-radius:50%;
    box-shadow:0 0 0 1px rgba(0,0,0,.08);
}

/* Décale un peu la carte interne pour éviter tout chevauchement visuel */
.tc-skin traction\:card{
    display:block;
    margin-left: 4px;                      /* petit jeu visuel */
    border-radius:8px;                     /* coins plus doux */
    border:1px solid #dfe3ea;
    overflow:hidden;
}

/* micro-ajustements de contenu interne (facultatif) */
.tc-skin input[type="text"],
.tc-skin input[type="search"],
.tc-skin input[type="tel"],
.tc-skin input[type="email"]{
    height:20px;
    padding:2px 6px;
    border-radius:6px;
}
.tc-skin > *{ position:relative; z-index:1; }
.tc-skin > :first-child{ margin-top:2px; }
.tc-skin > :last

