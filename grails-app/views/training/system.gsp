<%@ page import="traction.workflow.WorkflowData" contentType="text/html;charset=UTF-8" %>
<html>
<head>
	<meta name="layout" content="mainTraction"/>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<title><g:message code="system.training"/></title>
	
	<asset:javascript src="training/system.js"/>
	<asset:stylesheet src="training/system.css"/>
</head>

<body>
<!-- Onglets -->
<ul class="nav nav-tabs nav-pills m-4" role="tablist">
	<li class="nav-item">
		<button class="nav-link active" data-bs-toggle="tab" data-bs-target="#tabCardType" role="tab">
			<g:message code="cardtype.title"/>
		</button>
	</li>
	<li class="nav-item">
		<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabReservationColor" role="tab">
			<g:message code="reservation.color"/>
		</button>
	</li>
	<li class="nav-item">
		<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabIcon" role="tab">
			<g:message code="icon.icon"/>
		</button>
	</li>
	<li class="nav-item">
		<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabCalendarColor" role="tab">
			<g:message code="calendar"/>
		</button>
	</li>
	<li class="nav-item">
		<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabLabels" role="tab">
			<g:message code="opportunity.labels"/>
		</button>
	</li>
	<li class="nav-item">
		<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabSoldBoard" role="tab">
			<g:message code="main.soldboard"/>
		</button>
	</li>
	<li class="nav-item">
		<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabHistoryScore" role="tab">
			<g:message code="history.points"/>
		</button>
	</li>
	<li class="nav-item">
		<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabSource" role="tab">
			<g:message code="origin"/>
		</button>
	</li>
	<li class="nav-item">
		<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabMedia" role="tab">
			<g:message code="opportunity.media"/>
		</button>
	</li>
	<li class="nav-item">
		<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tabVehicleStatus" role="tab">
			<g:message code="vehicle.status"/>
		</button>
	</li>
</ul>

<div class="tab-content m-4">
	
	<!-- ==================== Légende / TRADE PS / BATEAUX VENDUS ==================== -->
	<div class="tab-pane fade show active" id="tabCardType" role="tabpanel">
		<h1 class="h4 fw-bold mb-3"><g:message code="system.information" default="Information sur le système"/></h1>
		
		<!-- Panneau "Légende du type de cartes" -->
		<div class="card">
			<div class="card-header">
				<span class="fw-semibold text-uppercase small">
					<g:message code="legend.cardtypes" default="LÉGENDE DU TYPE DE CARTES"/>
				</span>
			</div>
			<div class="card-body">
				
				<!-- Accordéon 1 : TRADE PS -->
				<div class="accordion accordion-flush" id="accordionTrade">
					<div class="accordion-item">
						<h2 class="accordion-header" id="headingTrade">
							<button class="accordion-button" type="button" data-bs-toggle="collapse"
							        data-bs-target="#collapseTrade" aria-expanded="true" aria-controls="collapseTrade">
								<g:message code="trade.ps" default="TRADE PS"/>
							</button>
						</h2>
						<div id="collapseTrade" class="accordion-collapse collapse show" aria-labelledby="headingTrade" data-bs-parent="#accordionTrade">
							<div class="accordion-body">
								
								<!-- Sous-accordéon : BATEAUX VENDUS (imbriqué) -->
								<div class="accordion accordion-flush" id="accordionBoats">
									<div class="accordion-item">
										<h2 class="accordion-header" id="headingBoats">
											<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
											        data-bs-target="#collapseBoats" aria-expanded="true" aria-controls="collapseBoats">
												<g:message code="boats.sold" default="BATEAUX VENDUS"/>
											</button>
										</h2>
										<div id="collapseBoats" class="accordion-collapse collapse show" aria-labelledby="headingBoats" data-bs-parent="#accordionBoats">
											<div class="accordion-body">
												
												<g:each var="workflow" in="${workflows}">
													<g:if test="${workflow?.workflow?.name}">
														<h2 class="h6 fw-bold mb-2">${workflow.workflow.name}</h2>
													</g:if>
													
													<!-- FLEX WRAP au lieu de grid -->
													<div class="legend-flex">
														<g:each var="type" in="${workflow.cardTypes}">
															<div class="legend-item">
																<div class="card h-100">
																	<div class="card-header py-2">
																		<div class="d-flex align-items-center gap-2">
																			<span class="badge rounded-pill bg-info-subtle text-info-emphasis">•</span>
																			<h6 class="mb-0 fw-bold small text-uppercase">${type.type.title}</h6>
																		</div>
																	</div>
																	<div class="card-body p-2">
																		<div class="card-body p-2">
																			<div class="tc-skin">
																				<traction:card noCache="true" opportunity="${type.opportunity}"/>
																			</div>
																		</div>
																	</div>
																</div>
															</div>
														</g:each>
													</div>
													<!-- /FLEX -->
												</g:each>
											
											</div>
										</div>
									
									</div>
								</div>
								<!-- Fin sous-accordéon -->
								
								<!-- Sous-accordéon : POWERSPORT -->
								<div class="accordion accordion-flush" id="accordionPowersport">
									<div class="accordion-item">
										<h2 class="accordion-header" id="headingPowersport">
											<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
											        data-bs-target="#collapsePowersport" aria-expanded="false" aria-controls="collapsePowersport">
												<g:message code="boats.powersport" default="POWERSPORT"/>
											</button>
										</h2>
										<div id="collapsePowersport" class="accordion-collapse collapse" aria-labelledby="headingPowersport" data-bs-parent="#accordionPowersport">
												<h1>POWERSPORT</h1>
										</div>

									</div>
								</div>
								<!-- Fin sous-accordéon -->
								
								<!-- Sous-accordéon : ENTREPOSAGE - BATEAUX 24/25 -->
								<div class="accordion accordion-flush" id="accordionEntreposage">
									<div class="accordion-item">
										<h2 class="accordion-header" id="headingEntreposage">
											<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
											        data-bs-target="#collapseEntreposage" aria-expanded="false" aria-controls="collapseEntreposage">
												<g:message code="boats.entreposage" default="ENTREPOSAGE - BATEAUX 24/25"/>
											</button>
										</h2>
										<div id="collapseEntreposage" class="accordion-collapse collapse" aria-labelledby="headingEntreposage" data-bs-parent="#accordionEntreposage">

										</div>

									</div>
								</div>
								<!-- Fin sous-accordéon -->
								
								
							
							</div>
						</div>
					</div>
				</div>
				<!-- Fin accordéon 1 -->
			</div>
		</div>
	</div>
	
	<!-- ==================== Autres onglets (Bootstrap pur) ==================== -->
	<div class="tab-pane fade" id="tabReservationColor" role="tabpanel">
		<h2 class="h5 fw-bold my-3"><g:message code="reservation.color"/></h2>
		<ul class="list-group">
			<g:each var="pendingColor" in="${pendingColors}">
				<li class="list-group-item d-flex align-items-center gap-2">
					<span class="mdi mdi-circle" style="color:${pendingColor.getColor()}"></span>
					${pendingColor.getTitle()}
				</li>
			</g:each>
		</ul>
	</div>
	
	<div class="tab-pane fade" id="tabIcon" role="tabpanel">
		<div class="row g-3">
			<g:each var="icon" in="${icons}">
				<div class="col-12 col-lg-4">
					<h2 class="h6 fw-bold my-2"><g:message code="${icon.message}"/></h2>
					<ul class="list-group">
						<g:each var="i" in="${icon.icons}">
							<li class="list-group-item">
								<span class="fw-semibold">${i.icon.title}</span>
								( ${raw(i.icon.html())} )
								<div class="mt-2">
									<traction:card noCache="true" opportunity="${i.opportunity}"/>
								</div>
							</li>
						</g:each>
					</ul>
				</div>
			</g:each>
		</div>
	</div>
	
	<div class="tab-pane fade" id="tabCalendarColor" role="tabpanel">
		<h2 class="h6 fw-bold my-3"><g:message code="card.calendar.explanation"/></h2>
		<ul class="list-group">
		<li class="list-group-item">
			<h6 class="fw-bold mb-2"><g:message code="card.4calendars"/> :</h6>
			<ul class="list-unstyled mb-2">
				<li><i class="material-icons text-success">web_asset</i> - <g:message code="card.4calendars.green"/></li>
				<li><i class="material-icons text-danger">web_asset</i> - <g:message code="card.4calendars.red"/></li>
				<li><i class="material-icons text-warning">web_asset</i> - <g:message code="card.4calendars.yellow"/></li>
				<li><i class="material-icons text-primary">web_asset</i> - <g:message code="card.4calendars.blue"/></li>
			</ul>
			
			<g:each var="color" in="${calendarColors}">
				<li class="list-group-item">
					<h6 class="fw-bold"><g:message code="${color.message}"/></h6>
					<traction:card noCache="true" opportunity="${color.opportunity}"/>
				</li>
			</g:each>
		</li>
		</ul>
	</div>
	
	<div class="tab-pane fade" id="tabLabels" role="tabpanel">
		<h2 class="h5 fw-bold my-3"><g:message code="opportunity.labels"/></h2>
		<div class="row row-cols-1 row-cols-md-2 row-cols-xl-3 g-3">
			<g:each var="label" in="${labels}">
				<div class="col">
					<div class="card h-100">
						<div class="card-header text-center py-2">
							<span class="fw-bold">${label.label.name}</span>
							<i class="mdi mdi-label ms-1" style="color:${label.label.color};"></i>
						</div>
						<div class="card-body p-2">
							<traction:card noCache="true" opportunity="${label.opportunity}"/>
						</div>
					</div>
				</div>
			</g:each>
		</div>
	</div>
	
	<div class="tab-pane fade" id="tabSoldBoard" role="tabpanel">
		<h2 class="h5 fw-bold my-3"><g:message code="main.soldboard"/></h2>
		
		<h3 class="h6 fw-bold"><g:message code="soldboard.colors"/></h3>
		<ul class="list-group mb-3">
			<li class="list-group-item d-flex align-items-center gap-2">
				<span class="badge rounded-pill" style="background-color:${traction.opportunity.CardBackgroundColorUtil.RED_TODAY};">&nbsp;</span>
				<i class="mdi mdi-arrow-right"></i>
				<g:message code="soldboard.red.explain"/>
			</li>
			<li class="list-group-item d-flex align-items-center gap-2">
				<span class="badge rounded-pill" style="background-color:${traction.opportunity.CardBackgroundColorUtil.YELLOW_LOST};">&nbsp;</span>
				<i class="mdi mdi-arrow-right"></i>
				<g:message code="soldboard.yellow.explain"/>
			</li>
			<li class="list-group-item d-flex align-items-center gap-2">
				<span class="badge rounded-pill" style="background-color:${traction.opportunity.CardBackgroundColorUtil.CYAN_ON_ORDER};">&nbsp;</span>
				<i class="mdi mdi-arrow-right"></i>
				<g:message code="soldboard.cyan.explain"/>
			</li>
			<li class="list-group-item d-flex align-items-center gap-2">
				<span class="badge rounded-pill" style="background-color:${traction.opportunity.CardBackgroundColorUtil.LIGHT_BLUE_IN_COTATION};">&nbsp;</span>
				<i class="mdi mdi-arrow-right"></i>
				<g:message code="soldboard.blue.explain"/>
			</li>
		</ul>
		
		<h3 class="h6 fw-bold"><g:message code="soldboard.goals.ranks"/></h3>
		<div id="goalsRankTable"></div>
		<script>
            $.get(tractionWebRoot + "/soldBoard/configRanksTable?hideDelete=true", function(html) {
                $('#goalsRankTable').html(html);
            });
		</script>
	</div>
	
	<div class="tab-pane fade" id="tabHistoryScore" role="tabpanel">
		<h2 class="h5 fw-bold my-3"><g:message code="history.points"/></h2>
		<div class="table-responsive">
			<table class="table table-striped table-bordered align-middle mb-0" id="historyScores">
				<thead class="table-light">
				<tr>
					<th><g:message code="actions"/></th>
					<th><g:message code="history.points"/></th>
				</tr>
				</thead>
				<tbody>
				<g:each var="hs" in="${historyScores}">
					<tr>
						<td><i class="${hs.status.icon} p-1 rounded" style="background-color:${hs.status.iconColor};"></i> - <g:message code="${hs.status.message}"/></td>
						<td>${hs.points}</td>
					</tr>
				</g:each>
				</tbody>
			</table>
		</div>
	</div>
	
	<div class="tab-pane fade" id="tabSource" role="tabpanel">
		<h2 class="h5 fw-bold my-3"><g:message code="origin"/></h2>
		<div class="table-responsive">
			<table class="table table-striped table-bordered mb-0" id="source">
				<tbody>
				<g:each var="o" in="${origins}">
					<tr>
						<td><i class="mdi mdi-web p-1 rounded" style="color:${o.color};"></i> - ${o.name}</td>
					</tr>
				</g:each>
				</tbody>
			</table>
		</div>
	</div>
	
	<div class="tab-pane fade" id="tabMedia" role="tabpanel">
		<h2 class="h5 fw-bold my-3"><g:message code="opportunity.media"/></h2>
		<div class="table-responsive">
			<table class="table table-striped table-bordered mb-0" id="media">
				<tbody>
				<g:each var="m" in="${medias}">
					<tr>
						<td><i class="${m.icon} p-1 rounded"></i> - <g:message code="${m.message}"/></td>
					</tr>
				</g:each>
				</tbody>
			</table>
		</div>
	</div>
	
	<div class="tab-pane fade" id="tabVehicleStatus" role="tabpanel">
		<h2 class="h5 fw-bold my-3"><g:message code="vehicle.vehicle.status"/></h2>
		<div class="table-responsive">
			<table class="table table-bordered align-middle mb-0" id="vehicleStatus">
				<thead class="table-light">
				<tr>
					<th><g:message code="display"/></th>
					<th><g:message code="badge"/></th>
					<th><g:message code="icon.icon"/></th>
					<th><g:message code="icon.color"/></th>
					<th><g:message code="form.element.backgroundColor"/></th>
					<th><g:message code="chat.widget.message"/></th>
				</tr>
				</thead>
				<tbody>
				<g:each var="status" in="${vehicleStatus}">
					<tr>
						<td class="text-nowrap">
							<i class="${status.icon}" style="color:${status.iconColor};"></i>
							<span class="ms-1"><g:message code="${status.message}"/></span>
						</td>
						<td class="text-nowrap">
							<span class="badge rounded-pill border"
							      style="background-color:${status.bgColor}; color:${status.iconColor}; border-color:${status.iconColor};">
								<i class="${status.icon}" style="color:${status.iconColor};"></i>
								<span class="ms-1"><g:message code="${status.message}"/></span>
							</span>
						</td>
						<td><i class="${status.icon}"></i></td>
						<td><span class="badge" style="background-color:${status.iconColor};">&nbsp;&nbsp;&nbsp;</span></td>
						<td><span class="badge" style="background-color:${status.bgColor};">&nbsp;&nbsp;&nbsp;</span></td>
						<td><g:message code="${status.message}"/></td>
					</tr>
				</g:each>
				</tbody>
			</table>
		</div>
	</div>

</div>
</body>
</html>
