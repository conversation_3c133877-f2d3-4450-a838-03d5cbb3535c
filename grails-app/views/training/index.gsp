<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="layout" content="mainTraction"/>
    <title><g:message code="training.title"/></title>
    <asset:stylesheet src="training/index.css"/>
    <asset:javascript src="training/index.js"/>
    <asset:javascript src="lib/jstree/jstree.js"/>
    <asset:stylesheet src="lib/jstree/style.css"/>
</head>
<body>
<div id="doc">
    <div class="m-4">
        ${raw(navigationBar)}
    </div>
        <div class="m-4">

            <g:each var="section" in="${sections.sort{ it.ordre }}">
                <div class="col-md-4 col-xs-6 card-container me-2">
                    <div class="card h-100 p-4">
	                    
	                    <div class="card supported-languages">
		                    <div class="card-header d-flex justify-content-between align-items-center">
			                    <span><g:message code="training.supportedLanguages" default="Langue supportée"/></span>
			                    
			                    <!-- Bouton Ajouter -->
			                    <button type="button" id="addLanguageBtn" class="btn btn-link p-0" aria-label="Ajouter">
				                    <i class="material-icons align-middle">add_circle</i>
			                    </button>
		                    </div>
		                    
		                    <ul class="list-group list-group-flush" id="langList">
			                    <g:each var="lang" in="${supportedLangs}">
				                    <li class="list-group-item d-flex align-items-center justify-content-between lang-item ${lang.code == defaultLang ? 'active' : ''}">
					                    <label class="d-flex align-items-center flex-grow-1 m-0">
						                    <input class="form-check-input me-2" type="radio" name="defaultLang"
						                           value="${lang.code}" ${lang.code == defaultLang ? 'checked' : ''}/>
						                    <span class="lang-name">${lang.label}</span>
					                    </label>
					                    
					                    <!-- Supprimer -->
					                    <button type="button" class="btn btn-link text-muted delete-lang" data-code="${lang.code}" aria-label="Supprimer">
						                    <i class="material-icons align-middle">delete</i>
					                    </button>
				                    </li>
			                    </g:each>
		                    </ul>
	                    </div>
	                    
	                    <div class="card-block">
                            <h4 class="card-title">${section.title}</h4>
                            <p class="card-text">${section.description}</p>

                        </div>
                        <a href="<g:createLink absolute="true" action="index" id="${section.id}"/>" class="btn btn-primary left-button"><g:message code="training.open.section"/></a>

                        <permission:ifHasPermissions permissions="[traction.permissions.Permission.PERM_TRAINING_WRITE]">
                            <g:if test="${alterable && sectionTraction != section}">
                                <i class="material-icons btn btn-primary right-icon" onclick="TractionModal.show({
                                    url: '/training/modaltrainingsection',
                                    data: {
                                        trainingSectionId: ${section.id},
                                        trainingParentId: ${section.parent},
                                        trainingSuperParentId: '${superParent?.id}'
                                    }
                                });">edit</i>
                            </g:if>
                        </permission:ifHasPermissions>
                    </div>
                </div>
            </g:each>

            <permission:ifHasPermissions permissions="[traction.permissions.Permission.PERM_TRAINING_WRITE]">
                <g:if test="${alterable}">
                    <div class="col-md-4 col-xs-6 card-container">
                        <div class="card h-100 new-card p-4">
                            <asset:image src="no_image.png" class="card-img-top img-fluid"/>
                            <div class="card-block">
                                <h4 class="card-title"><g:message code="training.new.title"/></h4>
                                <p class="card-text"><g:message code="training.new.description"/></p>
                            </div>
                            <button onclick="TractionModal.show({
                                url: '/training/modaltrainingsection',
                                data: {
                                    trainingParentId: ${parent?.id ?: 0},
                                    trainingSuperParentId: '${superParent?.id}'
                                }
                            });" class="btn btn-primary left-button"><g:message code="default.button.create.label"/></button>
                        </div>
                    </div>
                </g:if>
            </permission:ifHasPermissions>

        </div>

    <g:if test="${parent}">
        <div class="container-fluid">
            <div class="row g-0  border p-4 m-2">
                <g:each var="doc" in="${docs.sort{ it.ordre }}">
                    <div class="card w-100 mb-3">
                        <div class="card-block">
                            <h3 class="card-title">${doc.title}</h3>
                            <p class="card-text">${doc.description}</p>
                            <a href="<g:createLink absolute="true" action="doc" id="${doc.id}"/>" class="btn btn-primary"><g:message code="training.doc.open"/></a>
                        </div>
                    </div>
                </g:each>

                <permission:ifHasPermissions permissions="[traction.permissions.Permission.PERM_TRAINING_WRITE]">
                    <g:if test="${alterable}">
                        <div class="card w-100 new-card">
                            <div class="card-block">
                                <h3 class="card-title"><g:message code="training.doc.new.title"/></h3>
                                <p class="card-text"><g:message code="training.doc.new.description"/></p>
                                <button onclick="TractionModal.show({
                                    url: '/training/modaltrainingdoc',
                                    data: {
                                        trainingParentId: '${parent.id}'
                                    }
                                });" class="btn btn-primary"><g:message code="default.button.create.label"/></button>
                            </div>
                        </div>
                    </g:if>
                </permission:ifHasPermissions>

            </div>
        </div>
    </g:if>
</div>
</body>
</html>