package traction.security

import gorm.logical.delete.LogicalDelete
import grails.util.Holders
import groovy.transform.EqualsAndHashCode
import groovy.transform.ToString
import org.hibernate.sql.JoinType
import traction.BusinessUnit
import traction.DMS
import traction.ExternalIdentifier
import traction.assignmentRule.AssignmentRule
import traction.communication.CommunicationQueue
import traction.chat.ChatWidgetGroup
import traction.department.Department
import traction.department.DepartmentGroup
import traction.Extension
import traction.MailTemplate
import traction.UserGroup
import traction.facebook.FacebookPage
import traction.file.FileData
import traction.form.FormEvent
import traction.mobile.MobileConnection
import traction.notification.Notification
import traction.permissions.Permission
import traction.permissions.PermissionLevel
import traction.status.StatusBusy
import traction.taketurns.UserTurn
import traction.task.Meeting
import traction.task.Task
import traction.workflow.WorkflowAssigned

@EqualsAndHashCode(includes = 'username')
@ToString(includes = 'username', includeNames = true, includePackage = false)
class User implements Serializable, LogicalDelete<User> {
    static final List<String> filteredProperties = [
            "username",
            "firstname",
            "lastname",
            "email",
            "extID",
            "enabled",
            "status",
            "userGroup",
            "departments"
    ]

    static constraints = {
        password nullable: false, blank: false, password: true
        username nullable: false, blank: false, unique: true
        manager nullable: true
        authCode nullable: true
        image nullable: true
        lostPassword nullable: true
        userGroup nullable: true
        defaultFromEmail nullable: true
        redirectedUser nullable: true
    }

    static mapping = {
        version false
        status enumType: 'identity'
        statusChat enumType: 'identity'
        userGroup enumType: 'identity', index: 'userGroup_Idx'
        enabled sqlType: "int", length: 1
        accountExpired sqlType: "int", length: 1
        accountLocked sqlType: "int", length: 1
        passwordExpired sqlType: "int", length: 1
        password column: '`password`'
        defaultFromEmail index: 'defaultFromEmail_Idx', unique: true
        permissionLevel enumType: 'identity', index: 'permissionLevel_Idx', unique: false
        manager cascade: 'none'
        currentBusinessUnit cascade: 'none'
        currentDepartment cascade: 'none', lazy: false
        workflowAssigned cascade: 'none'
        businessUnits cascade: 'none'
        departments cascade: 'none'
        extensions cascade: 'none'
    }

    Date dateModif = new Date()
    Date dateSecurityModif = new Date() // date when passsword is edited
    private static final long serialVersionUID = 1
    String defaultFromEmail
    String username
    String password
    String firstname = ""
    String lastname = ""
    String color = ""
    String email = ""
    boolean enabled = true
    boolean accountExpired = false
    boolean accountLocked = false
    boolean passwordExpired = false
    boolean notifications = false
    boolean allManagerProductProfiles = false
    boolean allManagerProductView = false
    boolean allManagerProductFilter = false
    boolean visibleInSoldboard = true
    boolean signSmsWithName = true
    boolean notificationService = true
    boolean notificationDepartmentService = false
    /** Make calls private */
    boolean allPrivateCalls = false
    /** Make communications private */
    boolean allPrivateCommunications = false
    String authCode
    Date lostPassword
    StatusBusy status = StatusBusy.AVAILABLE
    StatusChat statusChat = StatusChat.NOTAVAILABLE
    User manager
    User redirectedUser
    FileData image
    UserGroup userGroup
    PermissionLevel permissionLevel = PermissionLevel.USER
    /**
     * BusinessUnit that the user is currently logged in
     */
    BusinessUnit currentBusinessUnit
    /**
     * Department that the user is currently logged in
     */
    Department currentDepartment
    UserSettings userSettings = new UserSettings()

    static hasMany = [
            workflowAssigned      : WorkflowAssigned,
            attributes            : UserAttribute,
            templates             : MailTemplate,
            businessUnits         : BusinessUnit,
            departments           : Department,
            mobileConnections     : MobileConnection,
            extensions            : Extension,
            externalIdentifiers   : ExternalIdentifier
    ]

    String getMobile() {
        return this.attributes.find { it.name == 'cell' }?.value
    }

    String getExtID(DMS dms, String query = "") {
        if (!dms) return ""
        ExternalIdentifier extId = externalIdentifiers.find { it.dmsId == dms.id && (!query || it.extId == query) }
        return extId ? extId.extId : ""
    }

    List<DepartmentGroup> getDepartmentGroups() {
        if (this.userGroup) {
            return this.departments.collect { it.getGroup(this.userGroup) }
        }
        return []
    }

    def getCurrentDepartmentGroup() {
        DepartmentGroup dg = this.currentDepartment.getGroup(this.userGroup)
        return dg
    }

    int getNotificationCount() {
        return Notification.createCriteria().count {
            eq("user", this)
            eq("read", false)
        }
    }

    /**
     * Get all Role of this user
     * @return Set of Role
     */
    Set<Role> getAuthorities() {
        (UserRole.findAllByUser(this) as List<UserRole>)*.role as Set<Role>
    }

    /**
     * Constructor
     */
    User(String username, String password) {
        this.username = username
        this.password = password
    }

    String getFullName() {
        if (this.firstname && this.lastname) return "${this.firstname} ${this.lastname}"
        return this.username ? this.username.capitalize() : ""
    }

    String getInitial() {
        String ret = this.username.take(2)
        if (this.firstname && this.lastname) {
            ret = this.firstname.take(1) + this.lastname.take(1)
        }
        return ret.toUpperCase()
    }

    /**
     * Return a string containing id, firstname, lastname and username of the User
     * Used for histories
     * @return User in a string
     */
    String toString() {
        return "${this.id}: ${this.firstname} ${this.lastname} (${this.username})"
    }

    enum StatusChat {
        AVAILABLE(0, 'status.user.available', 'mdi mdi-check-circle', '#9acd32'),
        NOTAVAILABLE(1, 'status.user.notavailable', 'mdi mdi-do-not-disturb-off', 'red'),

        final int id
        final String message
        final String icon
        final String iconColor

        private StatusChat(int id, String message, String icon, String iconColor) {
            this.id = id
            this.message = message
            this.icon = icon
            this.iconColor = iconColor
        }

        private static final Map<Integer, StatusChat> byId = new HashMap<Integer, StatusChat>()
        static {
            for (StatusChat e : StatusChat.values()) {
                if (byId.put(e.getId(), e) != null) {
                    throw new IllegalArgumentException("StatusChat duplicate id: " + e.getId())
                }
            }
        }

        static StatusChat getById(Integer id) {
            return byId.get(id)
        }

        String toString() {
            return this.message
        }
    }

    boolean isInChatWidgetGroupOrFacebookPage() {
        return ChatWidgetGroup.createCriteria().count {
            users {
                eq("id", this.id)
            }
        } || FacebookPage.createCriteria().count {
            users {
                eq("id", this.id)
            }
        } ? true : false
    }

    //USED IN MOBILE TOO!!!
    boolean isInPoolAssignmentRule() {
        return AssignmentRule.createCriteria().count {
            createAlias('users', '_users', JoinType.LEFT_OUTER_JOIN)
            createAlias('userGroups', '_userGroups', JoinType.LEFT_OUTER_JOIN)
            createAlias('departmentGroups', '_departmentGroups', JoinType.LEFT_OUTER_JOIN)
            eq("method", AssignmentRule.Method.POOL)
            eq("isActive", true)
            or {
                eq("_users.id", this.id)
                if (this.userGroup) eq("_userGroups.elements", this.userGroup)
                if (this.departmentGroups) 'in'("_departmentGroups.id", this.departmentGroups.id)
            }
        } ? true : false
    }

    Locale getLocale() {
        String language = "en"
        UserAttribute attribute = attributes.find { it.name == "language" }
        if (attribute) {
            language = attribute.value
        }
        return new Locale(language)
    }

    Map setNewStatus(StatusBusy status, UserTurn userTurn = null) {
        if (!userTurn) {
            userTurn = this.getLastUserTurn()
        }

        def securityService = Holders.grailsApplication.mainContext.getBean("securityService")
        if (userTurn && !securityService.hasPermission(Permission.PERM_TAKETURNS_ADMIN) && !this.hasLoggedMeeting(userTurn.lastTurn)) {
            return [
                    success: false,
                    message: "takeTurns.missing.meeting",
                    id     : userTurn?.takeTurns?.id
            ]
        }

        this.status = status
        if (userTurn) {
            switch (this.status) {
                case StatusBusy.VACATION:
                    userTurn.active = false
                    break
                case StatusBusy.INMEETING_NOT_PLANNED:
                    userTurn.newTurn()
            }
        }

        return [
                success: true,
                id     : userTurn?.takeTurns?.id
        ]
    }

    boolean hasLoggedMeeting(Date date) {
        if ([StatusBusy.INMEETING_NOT_PLANNED.id, StatusBusy.INMEETING_PLANNED.id].contains(this.status.id)) {
            List<Meeting> meetings = Meeting.createCriteria().list {
                ge("date", date)
                'in'("status", [Task.Status.DONE, Task.Status.NO_SHOW, Task.Status.CANCELED])
            }
            log.debug "${meetings}"
            return meetings.size() != 0
        }
        return true
    }

    Meeting nextMeeting() {
        List<Meeting> meets = Meeting.createCriteria().list {
            eq("owner", this)
            eq("status", Task.Status.TODO)
            if (this.getLastUserTurn()) ge("expectedDate", this.getLastUserTurn().lastTurn)
            order("expectedDate", "asc")
        }
        return meets ? meets.first() : null
    }

    UserTurn getLastUserTurn() {
        List<UserTurn> userTurns = UserTurn.findAllByUser(this)?.sort { it.lastTurn }
        return userTurns ? userTurns.last() : null
    }

    static User findByExtID(Long extId, DMS dms) {
        return findByExtID(extId?.toString(), dms)
    }

    static User findByExtID(String extId, DMS dms) {
        return ExternalIdentifier.findUser(dms, extId)
    }

    int getCommunicationQueueCount() {
        return CommunicationQueue.countByUser(this)
    }

    List<MailTemplate> getActiveTemplates() {
        return templates.findAll { it.active }.toList()
    }

    boolean isInAssignmentRuleUsersField() {
        return AssignmentRule.getAll().any { it.users?.contains(this) }
    }

    List<Permission> getPermissions() {
        List<Permission> permissions = []
        this.getAuthorities().each { Role role ->
            permissions += role.permissions.findAll { it.permission.allowedLevels.contains(permissionLevel) }.collect { it.permission }
        }
        return permissions
    }

    boolean isInEvent() {
        return FormEvent.getAll().any { it.notificationUsers?.contains(this) || it.users?.contains(this) }
    }
}
