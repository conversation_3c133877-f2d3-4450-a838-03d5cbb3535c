package traction

import grails.gorm.transactions.NotTransactional
import grails.gorm.transactions.Transactional
import traction.category.CategoryIcon
import traction.client.Client
import traction.client.IndividualClient
import traction.config.Icon
import traction.file.FileData
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityLabel
import traction.training.TrainingDoc
import traction.training.TrainingSection
import traction.workflow.WorkflowData

@Transactional
class TrainingService {
    
    def fileService
    def configService
    
    @NotTransactional
    def getSectionChilds(Long parent) {
        def ret = TrainingSection.createCriteria().list {
            eq("parent", parent)
            order("ordre")
        }
        return ret
    }
    
    @NotTransactional
    def getSectionChilds(String parent) {
        return getSectionChilds(parent as Long)
    }
    
    @NotTransactional
    def getSections() {
        return TrainingSection.getAll()
    }
    
    @NotTransactional
    def getHtmlHeaderLabel(TrainingSection section) {
        String ret = ""
        String icon = '<i class="material-icons">keyboard_arrow_right</i>'
        String url = configService.get("SERVER", "server.traction.base")
        if (section) {
            for (;;) {
                if (ret.equals("")) {
                    ret = '<a href="' + url + '/training/index/' + section.id + '" class="badge bg-primary">'+(section.title.size() < 20 ? section.title : section.title.take(20)+'...')+'</a>' + ret
                }
                else {
                    ret = '<a href="' + url + '/training/index/' + section.id + '" class="badge bg-info">'+(section.title.size() < 20 ? section.title : section.title.take(20)+'...')+'</a>' + icon + ret
                }
                if (section.parent == 0) break;
                section = getSection(section.parent)
            }
        }
        ret = '<a href="' + url + '/training/index" class="badge bg-info"><i class="material-icons">people</i></a>' + icon + ret
        return ret
    }

    @NotTransactional
    def getAllSuperParent() {
        def ret = TrainingSection.findAllByParent(0)
        def tractions = getTractionSuperSections()
        tractions.each {
            ret.removeElement(it)
        }
        return ret
    }
    
    @NotTransactional
    def getDoc(Long id) {
        return TrainingDoc.get(id)
    }
    
    @NotTransactional
    def getDoc(String id) {
        return getDoc(Long.valueOf(id))
    }
    
    @NotTransactional
    def getSection(Long id){
        return TrainingSection.get(id)
    }
    
    @NotTransactional
    def getSection(String id){
        return getSection(id as long)
    }
    
    def deleteSection(TrainingSection section){
        log.debug "traningService.deleteSection "+section
        def ret = [success: false, message: "training.section.error.notexist"]
        if (section) {
            def childs = getSectionChilds(section.id)
            if (childs || section.trainingDocs) {
                ret.message = "training.section.error.childs"
            }
            else {
                // Delete the section
                if (section.image) {
                    FileData file = section.image
                    section.image = null
                    fileService.delete(file)
                }
                section.superParent = null
                section.delete(flush:true)
            
                // Reorder other sections with same parent
                def ordre = section.ordre
                def sections = getSectionChilds(section.parent)
                orderSet(sections)
                ret.success = true
                ret.message = "training.section.deleted"
            }
        }
        return ret
    }
    
    def deleteDoc(TrainingDoc doc){
        log.debug "traningService.deleteDoc" + doc
        def ret = [success: false, message: "training.doc.error.notexist"]
        if (doc) {
            def ordre = doc.ordre
            def parent = doc.trainingSection
            def files = doc.files
            
            if (files) {
                ret.message = "training.doc.error.childs"
            }
            else {
                doc.delete(flush:true)
                // Reorder othe docs on the section
                def docs = parent.trainingDocs.find{ it.ordre > ordre }
                docs.each {
                    it.ordre = it.ordre - 1
                    it.save(flush:true)
                }
                files.each {
                    fileService.delete(it)
                }
                ret.success = true
                ret.message = "training.doc.deleted"
            }
        }
        return ret
    }
    
    def saveSection(TrainingSection section) {
        def ret = [success: false, message: "null"]
        if (section) {
            if (section.title.equals("") == false) {
                if (section.description.equals("") == false) {
                    if (section.ordre > 0) {
                        GormEntityUtils.save(section)
                        ret = [success: true, message: "training.section.saved"]
                    }
                    else {
                        ret.message = "Invalid order."
                    }
                }
                else {
                    ret.message = "training.doc.error.description"
                }
            }
            else {                        
                ret.message = "training.doc.error.title"
            }
        }
        return ret
    }

    @NotTransactional
    def isAlterable(TrainingSection section) {
        def altrable = true
        def configs = Config.findAllByCategoryAndName("TRAINING", "SECTION_TRACTION")
        if (configs) {
            configs.each {
                TrainingSection tractionSection = TrainingSection.get(it.value)
                if (tractionSection && section) {
                    if (isInChilds(section, tractionSection.id)) {
                        log.debug "isAlterable:false"
                        altrable = false
                    }
                }
            }
        }
        return altrable
    }

    @NotTransactional
    def getDocsTreeJSON(TrainingSection superParent) {
        def ret = []
        if (superParent) {
            getSectionChilds(superParent.id).each {
                int nbDocs = it.trainingDocs ? it.trainingDocs.size() : 1
                def map = [id: it.id, text: it.title, a_attr: ["nb-docs": nbDocs, ondblclick: "selectDocParent(this)"]]
                def children = getDocsTreeJSON(it)
                if (children.size() > 0) {
                    map.children = children
                }
                ret.add(map)
            }
        }
        return ret
    }


    @NotTransactional
    def getSectionsTreeJSON(TrainingSection superParent, Long currentSectionId) {
        def ret = []
        if (superParent) {
            getSectionChilds(superParent.id).each {
                if (it.id != currentSectionId) {
                    def childs = getSectionChilds(it.id)
                    int nbSections = getSectionChilds(it.id).size()
                    if (!childs.id.contains(currentSectionId)) {
                        nbSections++
                    }
                    def map = [id: it.id, text: it.title, a_attr: ["nb-sections": nbSections, ondblclick: "selectSectionParent(this)"]]
                    def children = getSectionsTreeJSON(it, currentSectionId)
                    if (children.size() > 0) {
                        map.children = children
                    }
                    ret.add(map)
                }
            }
        }
        else {
            ret.add([id:0, text:'All', a_attr: ["nb-sections": getAllSuperParent().size(), ondblclick: "selectSectionParent(this)"]])
        }
        return ret
    }

    @NotTransactional
    def getTractionSuperSections() {
        def ret = []
        def configs = Config.findAllByCategoryAndName("TRAINING", "SECTION_TRACTION")
        if (configs) {
            configs.each {
                TrainingSection tractionSection = TrainingSection.get(it.value)
                if (tractionSection) {
                    ret.add(tractionSection)
                }
            }
        }
        return ret 
    }

    @NotTransactional
    def getTractionSuperSection(String lang) {
        def val = configService.get("TRAINING", "SECTION_TRACTION", lang)
        if (val.equals("") == false) {
            return TrainingSection.get(val)
        }
        return null 
    }

    @NotTransactional
    def isAlterable(TrainingDoc doc) {
        TrainingSection tractionSection = getTractionSuperSection()
        if (tractionSection && doc) {
            return !isInChilds(doc.trainingSection, tractionSection.id)
        }
        return true
    }
    
    def saveDoc(TrainingDoc doc) {
        def ret = [success: false, message: "null"]
        if (doc) {
            if (doc.title.equals("") == false) {
                if (doc.description.equals("") == false) {
                    if (doc.ordre > 0) {
                        if (doc.trainingSection) {
                            if (GormEntityUtils.save(doc)) {
                                ret = [success: true, message: "training.doc.saved"]
                            }
                            else {
                                ret.message = "Error."
                            }
                        }
                        else {
                            ret.message = "Document must be part of a training section."
                        }
                    }
                    else {
                        ret.message = "Invalid order."
                    }
                }
                else {
                    ret.message = "training.doc.error.description"
                }
            }
            else {                        
                ret.message = "training.doc.error.title"
            }
        }
        return ret
    }
    
    // Check if isIn is in any child of section with id = parent
    @NotTransactional
    def isInChilds(TrainingSection isIn, Long parent) {
        if (isIn) {
            // Get parent childs
            def childSections = getSectionChilds(parent)
            childSections = childSections ? childSections : []

            // Check if parent == IsIn or if parent children contains IsIn
            if (isIn.id == parent || childSections.contains(isIn)) {
                return true
            }
            else {
                // Check for every children of parent
                childSections.each {
                    if (isInChilds(isIn, it.id)) {
                        return true
                    }
                }
            }
        }
        return false
    }
    
    def orderSet(def objects) {
        if (objects) {
            int o = 1;
            objects = objects.sort{ it.ordre }
            objects.each {
                if (it.ordre != o) {
                    it.ordre = o
                    GormEntityUtils.save(it)
                }
                log.debug "SAVED:" + o
                o++;
            }
        }
    }

    @NotTransactional
    Opportunity getDemoOpportunity(Long typeOfCard, Icon icon, OpportunityLabel label) {
        Client client = new IndividualClient(
                firstname: "Demo",
                lastname: "client",
                email: "<EMAIL>",
                roopen: "RO-OPEN"
        )
        WorkflowData workflowData = new WorkflowData(
                typeOfCard: typeOfCard
        )
        if (icon) {
            def category = CategoryIcon.getAll().find { it.message == icon.category }
            if (category && category.property) {
                workflowData.setProperty(category.property, icon.id)
            }
        }
        Opportunity opportunity = new Opportunity(
                name: "Product",
                stocknumber: "stocknumber",
                client: client,
                workflowData: workflowData
        )
        if (label) {
            opportunity.addToLabels(label)
        }
        return opportunity
    }
}
