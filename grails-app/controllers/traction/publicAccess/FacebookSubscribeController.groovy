package traction.publicAccess

import grails.plugin.springsecurity.annotation.Secured
import traction.ExecutionTime

@Secured('permitAll')
class FacebookSubscribeController {

    def facebookMessageService

    def message() {
        ExecutionTime time = new ExecutionTime()
        log.debug "message:${request.JSON}"
        Map json = request.JSON
        if (json) {
            json.entry.each {
                facebookMessageService.sync(it.messaging[0])
            }
        }
        render(status: 200)
        log.debug "FACEBOOK WEBHOOK time.executionTime:" + time.executionTime()
        if (time.executionTime() > 20000) {
            log.warn "FacebookSubscribe Webhook too slow (more than 20sec). params: ${params} json: ${request.JSON}"
        }
    }
}
