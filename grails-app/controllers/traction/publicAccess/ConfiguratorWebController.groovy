package traction.publicAccess

import grails.plugin.springsecurity.annotation.Secured
import traction.ConfigService
import traction.opportunity.Opportunity

@Secured('ROLE_USER')
class ConfiguratorWebController {

    ConfigService configService

    @Secured('permitAll')
    def index() {
        log.debug "index configurator: ${params}"
        String traction = configService.get("SERVER", "server.traction.base")
        String base = traction.replaceAll("\\/traction.*", "")
        render(view: "index", model: [base: base, traction: traction])
    }

    def traction() {
        log.debug "traction configurator: ${params}"
        Opportunity opportunity
        if (params.opportunityId) {
            opportunity = Opportunity.get(params.opportunityId as long)
        }
        String configurator1 = params.configurator1 ?: "CONFIGURABLE"
        String configurator2 = params.configurator2 ?: "CONFIGURABLE"
        String tractionUrl = configService.get("SERVER", "server.traction.base")
        String generalUrl = configService.get("SERVER", "server.general.base")
        render(view: "traction", model: [
                type: params.type,
                opportunity: opportunity,
                tractionUrl: tractionUrl,
                generalUrl: generalUrl,
                configurator1: configurator1,
                configurator2: configurator2
        ])
    }
}