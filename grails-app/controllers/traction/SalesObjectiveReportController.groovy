package traction

import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import org.grails.web.json.JSONObject
import org.hibernate.sql.JoinType
import org.springframework.web.servlet.support.RequestContextUtils
import traction.client.ClientInterest
import traction.department.Department
import traction.history.HistoryOpportunity
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityAssigned

import java.text.DateFormat
import java.text.SimpleDateFormat

// TODO: A revoir avec Kevin, pour le moment non fonctionnel. DV1-T543
//@Secured('ROLE_USER')
@Secured("@securityService.secured('PERM_SALES_OBJECTIVE_REPORT')")
class SalesObjectiveReportController {

    SalesObjectiveReportService salesObjectiveReportService
    def vehicleSelectionOptionService

    def index() {
        Date to = new Date()
        Date from = to - 365
        String fromToInput = "${from.format("MM/dd/yyyy")} - ${to.format("MM/dd/yyyy")}"

        List makes = vehicleSelectionOptionService.getMakes().collect { it.label } as List<String>
        List interests = ClientInterest.getAll()
        List departments = Department.getAll()
        List<Opportunity.Status> soldList = Opportunity.Category.SOLD.status.findAll { !it.isLostSold() }
        List<Opportunity.Status> lostList = Opportunity.Category.SOLD.status.findAll { it.isLostSold() }

        render(view: "index", model: [makes: makes, interests: interests, soldList: soldList, lostList: lostList, fromToInput: fromToInput, departments: departments,])
    }

    def dataTable() {
        Calendar c = Calendar.getInstance()
        c.set(Calendar.MILLISECOND, 0)
        c.set(Calendar.SECOND, 0)
        c.set(Calendar.MINUTE, 0)
        c.set(Calendar.HOUR_OF_DAY, 0)
        c.set(Calendar.DAY_OF_MONTH, 1)
        c.add(Calendar.YEAR, 1)
        List<Map> months = (0..(params.int("months") - 1)).collect {
            Locale locale = RequestContextUtils.getLocale(request)
            [
                    name : c.getDisplayName(Calendar.MONTH, Calendar.LONG, locale).capitalize() + " " + c.get(Calendar.YEAR),
                    nameShort: c.getDisplayName(Calendar.MONTH, Calendar.SHORT, locale).capitalize(),
                    index: c.add(Calendar.MONTH, -1) ?: it
            ]
        }
        render(template: "dataTable", model: [months: months, id: params.id, method: params.method])
    }

    def getData() {
        log.debug "params:${params}"

        String method = params.method

        JSONObject filter = new JSONObject()
        filter.put("makes", params["filter[makes]"] ? params["filter[makes]"].toString().split(",").collect { Long.parseLong(it) } : null)
        filter.put("interests", params["filter[interests]"] ? params["filter[interests]"].toString().split(",").collect { Long.parseLong(it) } : null)
        filter.put("departments", params["filter[departments]"] ? params["filter[departments]"].toString().split(",").collect { Long.parseLong(it) } : null)
        filter.put("status", params["filter[status]"] ? params["filter[status]"].toString().split(",").collect { Opportunity.Status.getById(Integer.parseInt(it)) } : null)

        ClientInterest interest = ClientInterest.findByName(params.interest)

        String orderCol = params["columns[" + params["order[0][column]"] + "][data]"]
        String orderDir = params["order[0][dir]"]

        List data = salesObjectiveReportService.getData(method, interest, params.make, filter, request.locale)
        data.sort { orderCol == method ? it[orderCol] : it[orderCol].now }
        Map json = [
                draw: params.draw,
                data: orderDir == "asc" ? data : data.reverse()
        ]
        render json as JSON
    }

    def statsObjective() {
        log.debug "statsObjective:" + params
        Date from = Date.parse("yyyy-MM-dd", params.from)
        Date to = Date.parse("yyyy-MM-dd", params.to) + 1

        render(template: "statsObjective", model: [from: from, to: to, departments: params.departments, interests: params.interests, makes: params.makes, status: params.status, by: params.by])
    }

    def getBigChart() {
        log.debug "getBigChart:" + params
        HistoryOpportunity.Type historyTypeEnter = HistoryOpportunity.Type.ENTER_CATEGORY
        HistoryOpportunity.Type historyTypeSold = HistoryOpportunity.Type.EXIT_CATEGORY_SOLD

        Map jsonData = [:]
        if (params.from && params.to) {
            // Parse params
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)
            List<Long> departments = params.departments ? params.departments.split(',').collect { it as long } : null
            List<Long> interests = params.interests ? params.interests.split(',').collect { it as long } : null
            List<String> makes = salesObjectiveReportService.splitMake(params.makes)
            List<Opportunity.Status> status = params.status ? params.status.split(',').collect { Opportunity.Status.getById(Integer.parseInt(it)) } : null

            // Set base calendars
            Calendar fromCalendar = Calendar.getInstance();
            fromCalendar.setTime(from)
            Calendar toCalendar = Calendar.getInstance();
            toCalendar.setTime(to)
            // Check if dates are valid
            if (fromCalendar < toCalendar) {
                // Set the calendar at the start of week/month
                fromCalendar.set(Calendar.DAY_OF_MONTH, 1)
                // Set the values for incremetation on dates by a month/week
                int calendarIncrementProperty = Calendar.MONTH
                int calendarIncrementAmount = 1

                List<String> labels = []
                // Build the time labels
                for (; ;) {
                    DateFormat formatter = new SimpleDateFormat("MMMM yyyy", request.locale);
                    String s = formatter.format(fromCalendar.getTime())
                    labels.add(s.substring(0, 1).toUpperCase() + s.substring(1))
                    fromCalendar.add(calendarIncrementProperty, calendarIncrementAmount)
                    if (fromCalendar > toCalendar) break
                }
                jsonData.labels = labels

                // Reset the from calendar
                fromCalendar.setTime(from)
                fromCalendar.set(Calendar.DAY_OF_MONTH, 1)

                Map datasetsMap = [
                        'objective'    : [],
                        'objectiveLast': [],
                        'enter'        : [],
                        'enterLast'    : [],
                        'sold'         : [],
                        'soldLast'     : [],
                        'ratio'        : [],
                        'ratioLast'    : []
                ]

                List historyDatas = HistoryOpportunity.createCriteria().list {
                    createAlias("opportunity", "_o")
                    createAlias("_o.vehicle", "_v")
                    ge("date", fromCalendar.getTime() - 365)
                    le("date", toCalendar.getTime())
                    or {
                        eq("type", historyTypeEnter)
                        and {
                            if (status) {
                                or {
                                    status.each {
                                        eq("_o.status", it)
                                    }
                                }
                            }
                            eq("type", historyTypeSold)
                        }
                    }
                    eq("category", Opportunity.Category.ACTIVE)
                    if (makes && makes.size() > 0 && makes[0].size() > 0) {
                        'in'("_v.make", makes)
                    }
                    /*
                    if (interests) {
                        createAlias("_v.interest", "_i", JoinType.LEFT_OUTER_JOIN)
                        'in'("_i.id", interests)
                    }
                     */
                    if (departments) {
                        createAlias("_o.assigned", "_a")
                        createAlias("_a.user", "_u")
                        createAlias("_u.departments", "_d")
                        isNotNull("userSales")
                        eq("_a.type", OpportunityAssigned.Type.PRIMARY)
                        eq("_u.userGroup", UserGroup.SALES)
                        'in'("_d.id", departments)
                    }
                    projections {
                        groupProperty("year")
                        groupProperty("month")
                        groupProperty("type")
                        rowCount()
                    }
                }

                Map dataMap = [:]
                historyDatas.each {
                    int year = it[0]
                    int month = it[1]
                    HistoryOpportunity.Type type = it[2]
                    int count = it[3]
                    dataMap["${year}-${month}-${type.id}"] = count
                }

                List historyObjectiveDatas = ObjectiveComparator.createCriteria().list {
                    createAlias("interest", "_i", JoinType.LEFT_OUTER_JOIN)
                    createAlias("department", "_d", JoinType.LEFT_OUTER_JOIN)
                    isNotNull("date")
                    switch (params.by) {
                        case "make":
                            isNotNull("make")
                            isNull("interest")
                            break
                        case "interest":
                            isNotNull("interest")
                            isNull("make")
                            break
                    }
                    if (makes) {
                        'in'("make", makes)
                    }
                    if (interests) {
                        'in'("_i.id", interests)
                    }
                    if (departments) {
                        'in'("_d.id", departments)
                    }
                    projections {
                        groupProperty("year")
                        groupProperty("month")
                        sum("value")
                    }
                }

                Map dataObjectiveMap = [:]
                historyObjectiveDatas.each {
                    int year = it[0]
                    int month = it[1]
                    int sum = it[2]
                    dataObjectiveMap["${year}-${month}"] = sum
                }

                Calendar c = fromCalendar
                while (c < toCalendar) {
                    int month = c.get(Calendar.MONTH) + 1
                    int year = c.get(Calendar.YEAR)
                    int lastYear = c.get(Calendar.YEAR) - 1

                    int enter = dataMap["${year}-${month}-${historyTypeEnter.id}"] ?: 0
                    int sold = dataMap["${year}-${month}-${historyTypeSold.id}"] ?: 0
                    int enterLast = dataMap["${lastYear}-${month}-${historyTypeEnter.id}"] ?: 0
                    int soldLast = dataMap["${lastYear}-${month}-${historyTypeSold.id}"] ?: 0

                    int objective = dataObjectiveMap["${year}-${month}"] ?: 0
                    int objectiveLast = dataObjectiveMap["${lastYear}-${month}"] ?: 0

                    datasetsMap['objective'] << objective
                    datasetsMap['objectiveLast'] << objectiveLast
                    datasetsMap['ratio'] << Math.round(salesObjectiveReportService.ratio(sold, enter) * 10000) / 100
                    datasetsMap['ratioLast'] << Math.round(salesObjectiveReportService.ratio(soldLast, enterLast) * 10000) / 100
                    datasetsMap['enter'] << enter
                    datasetsMap['enterLast'] << enterLast
                    datasetsMap['sold'] << sold
                    datasetsMap['soldLast'] << soldLast

                    c.add(Calendar.MONTH, 1)
                }

                jsonData.datasetsMap = datasetsMap
            }
        }
        render jsonData as JSON
    }

    def getSmallChart() {
        log.debug "getSmallChart: ${params}"
        HistoryOpportunity.Type historyTypeEnter = HistoryOpportunity.Type.ENTER_CATEGORY
        HistoryOpportunity.Type historyTypeSold = HistoryOpportunity.Type.EXIT_CATEGORY_SOLD

        Map jsonData = [:]
        if (params.from && params.to) {
            // Parse params
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)
            List<Long> departments = params.departments ? params.departments.split(',').collect { it as long } : null
            List<Long> interests = params.interests ? params.interests.split(',').collect { it as long } : null
            List<String> makes = salesObjectiveReportService.splitMake(params.makes)
            List<Opportunity.Status> status = params.status ? params.status.split(',').collect { Opportunity.Status.getById(Integer.parseInt(it)) } : null

            // Set base calendars
            Calendar fromCalendar = Calendar.getInstance();
            fromCalendar.setTime(from)
            Calendar toCalendar = Calendar.getInstance();
            toCalendar.setTime(to)
            if (fromCalendar < toCalendar) {
                // Set the calendar at the start of week/month
                fromCalendar.set(Calendar.DAY_OF_MONTH, 1)

                Map datasetsMap = [
                        'objective': [],
                        'sold'     : [],
                ]
                List<String> allLabels = []
                switch (params.type) {
                    case "interest":
                        return render(status: 403)
                        ClientInterest.getAll().sort { it.name }.each {
                            allLabels.add(it.name)
                        }
                        break
                    case "make":
                        vehicleSelectionOptionService.getMakes().sort { it.label }.each {
                            allLabels.add(it.label)
                        }
                        break
                    case "isUsed":
                        allLabels.addAll([g.message(code: "vehicle.new"), g.message(code: "vehicle.used")])
                        break
                }
                List historyDatas = HistoryOpportunity.createCriteria().list {
                    createAlias("opportunity", "_o")
                    createAlias("_o.vehicle", "_v")
                    //createAlias("_v.interest", "_i", JoinType.LEFT_OUTER_JOIN)
                    ge("date", fromCalendar.getTime())
                    le("date", toCalendar.getTime())
                    and {
                        eq("type", historyTypeSold)
                        if (status) {
                            or {
                                status.each {
                                    eq("_o.status", it)
                                }
                            }
                        }
                    }
                    eq("category", Opportunity.Category.ACTIVE)
                    if (params.type != "isUsed") {
                        eq("_v.isUsed", false)
                    }
                    if (makes && makes.size() > 0 && makes[0].size() > 0) {
                        'in'("_v.make", makes)
                    }
                    /*
                    if (interests) {
                        'in'("_i.id", interests)
                    }
                     */
                    if (departments) {
                        createAlias("_o.assigned", "_a")
                        createAlias("_a.user", "_u")
                        createAlias("_u.departments", "_d")
                        isNotNull("userSales")
                        eq("_a.type", OpportunityAssigned.Type.PRIMARY)
                        eq("_u.userGroup", UserGroup.SALES)
                        'in'("_d.id", departments)
                    }
                    projections {
                        switch (params.type) {
                            case "make":
                                groupProperty("_v.make")
                                break
                            case "interest":
                                //groupProperty("_i.name")
                                break
                            case "isUsed":
                                groupProperty("_v.isUsed")
                                break
                        }
                        rowCount()
                    }
                }

                Map dataMap = [:]
                historyDatas.each {
                    log.debug "historyData: ${it}"
                    String propertyType = it[0] && it[0].class == Boolean ? (it[0] == true ? g.message(code: "vehicle.used") : g.message(code: "vehicle.new")) : it[0]
                    int count = it[1]
                    dataMap["${propertyType}"] = count
                }

                List historyObjectiveDatas = ObjectiveComparator.createCriteria().list {
                    createAlias("interest", "_i", JoinType.LEFT_OUTER_JOIN)
                    createAlias("department", "_d", JoinType.LEFT_OUTER_JOIN)
                    isNotNull("date")
                    ge("date", fromCalendar.getTime())
                    le("date", toCalendar.getTime())
                    if (makes && makes.size() > 0 && makes[0].size() > 0) {
                        'in'("make", makes)
                    }
                    if (interests) {
                        'in'("_i.id", interests)
                    }
                    switch (params.type) {
                        case "make":
                            isNotNull("make")
                            isNull("interest")
                            isNull("isUsed")
                            break
                        case "interest":
                            isNotNull("interest")
                            isNull("make")
                            isNull("isUsed")
                            break
                        case "isUsed":
                            isNotNull("isUsed")
                            isNull("make")
                            isNull("interest")
                            break
                    }
                    if (departments) {
                        'in'("_d.id", departments)
                    }
                    projections {
                        groupProperty(params.type)
                        sum("value")
                    }
                }

                Map dataObjectiveMap = [:]
                historyObjectiveDatas.each {
                    String propertyType = it[0].class == Boolean ? (it[0] ? g.message(code: "vehicle.used") : g.message(code: "vehicle.new")) : it[0].name
                    int sum = it[1]
                    dataObjectiveMap["${propertyType}"] = sum
                }

                List<String> labels = []
                allLabels.each { label ->
                    int sold = dataMap["${label}"] ?: 0
                    int objective = dataObjectiveMap["${label}"] ?: 0

                    if (sold != 0 || objective != 0) {
                        datasetsMap['objective'] << objective
                        datasetsMap['sold'] << sold

                        labels.add(label)
                    }
                }
                jsonData.labels = labels

                jsonData.datasetsMap = datasetsMap
            }
        }

        render jsonData as JSON
    }

    def getPieChart() {
        log.debug "getPieChart: ${params}"
        HistoryOpportunity.Type historyTypeSold = HistoryOpportunity.Type.EXIT_CATEGORY_SOLD

        Map jsonData = [:]
        if (params.from && params.to && params.type && params.method) {
            // Parse params
            Date from = Date.parse("yyyy-MM-dd", params.from)
            Date to = Date.parse("yyyy-MM-dd", params.to)
            List<Long> departments = params.departments ? params.departments.split(',').collect { it as long } : null
            List<Long> interests = params.interests ? params.interests.split(',').collect { it as long } : null
            List<String> makes = salesObjectiveReportService.splitMake(params.makes)
            List<Opportunity.Status> status = params.status ? params.status.split(',').collect { Opportunity.Status.getById(Integer.parseInt(it)) } : null

            // Set base calendars
            Calendar fromCalendar = Calendar.getInstance();
            fromCalendar.setTime(from)
            Calendar toCalendar = Calendar.getInstance();
            toCalendar.setTime(to)
            if (fromCalendar < toCalendar) {
                // Set the calendar at the start of week/month
                fromCalendar.set(Calendar.DAY_OF_MONTH, 1)

                Map datasetsMap = [
                        'backgroundColors': [],
                        'borderColors'    : [],
                        'borderWidths'    : [],
                        'sold'            : [],
                ]

                List<String> allLabels = []
                switch (params.type) {
                    case "interest":
                        return render(status: 403)
                        ClientInterest.getAll().sort { it.name }.each {
                            allLabels.add(it.name)
                        }
                        break
                    case "make":
                        vehicleSelectionOptionService.getMakes().sort { it.label }.each {
                            allLabels.add(it.label)
                        }
                        break
                    case "used":
                        allLabels.addAll([g.message(code: "vehicle.new"), g.message(code: "vehicle.used")])
                        break
                }

                List historyDatas = []
                switch (params.method) {
                    case "objective":
                        historyDatas = ObjectiveComparator.createCriteria().list {
                            createAlias("interest", "_i", JoinType.LEFT_OUTER_JOIN)
                            createAlias("department", "_d", JoinType.LEFT_OUTER_JOIN)
                            isNotNull("date")
                            ge("date", fromCalendar.getTime())
                            le("date", toCalendar.getTime())
                            if (makes && makes.size() > 0 && makes[0].size() > 0) {
                                'in'("make", makes)
                            }
                            if (interests) {
                                'in'("_i.id", interests)
                            }
                            switch (params.type) {
                                case "make":
                                    isNotNull("make")
                                    isNull("interest")
                                    break
                                case "interest":
                                    isNotNull("interest")
                                    isNull("make")
                                    break
                            }
                            if (departments) {
                                'in'("_d.id", departments)
                            }
                            projections {
                                switch (params.type) {
                                    case "make":
                                        groupProperty("make")
                                        break
                                    case "interest":
                                        groupProperty("_i.name")
                                        break
                                }
                                sum("value")
                            }
                        }
                        break
                    case "opportunity":
                        historyDatas = HistoryOpportunity.createCriteria().list {
                            createAlias("opportunity", "_o")
                            createAlias("_o.vehicle", "_v")
                            //createAlias("_v.interest", "_i", JoinType.LEFT_OUTER_JOIN)
                            ge("date", fromCalendar.getTime())
                            le("date", toCalendar.getTime())
                            and {
                                eq("type", historyTypeSold)
                                if (status) {
                                    or {
                                        status.each {
                                            eq("_o.status", it)
                                        }
                                    }
                                }
                            }
                            eq("category", Opportunity.Category.ACTIVE)
                            if (params.type != "used") {
                                eq("_v.isUsed", false)
                            }
                            if (makes && makes.size() > 0 && makes[0].size() > 0) {
                                'in'("_v.make", makes)
                            }
                            /*
                            if (interests) {
                                'in'("_i.id", interests)
                            }
                             */
                            if (departments) {
                                createAlias("_o.assigned", "_a")
                                createAlias("_a.user", "_u")
                                createAlias("_u.departments", "_d")
                                isNotNull("userSales")
                                eq("_a.type", OpportunityAssigned.Type.PRIMARY)
                                eq("_u.userGroup", UserGroup.SALES)
                                'in'("_d.id", departments)
                            }
                            projections {
                                switch (params.type) {
                                    case "make":
                                        groupProperty("_v.make")
                                        break
                                    case "interest":
                                        //groupProperty("_i.name")
                                        break
                                    case "used":
                                        groupProperty("_v.isUsed")
                                        break
                                }
                                rowCount()
                            }
                        }
                        break
                }
                log.debug "pie data: ${historyDatas}"
                log.debug "difference: ${params.type} | ${params.method}"

                Map dataMap = [:]
                historyDatas.each {
                    String propertyType = it[0]
                    if (params.type == "used" && propertyType == "false") {
                        propertyType = g.message(code: "vehicle.new")
                    } else if (params.type == "used" && propertyType == "true") {
                        propertyType = g.message(code: "vehicle.used")
                    }
                    int count = it[1]
                    dataMap["${propertyType}"] = count
                }

                log.debug "dataMap: ${dataMap}"

                Random rand = new Random()

                List<String> labels = []
                allLabels.each { label ->
                    int sold = dataMap["${label}"] ?: 0

                    if (sold != 0) {
                        String color = String.format("#%x", rand.nextInt()).substring(0, 7)
                        datasetsMap['sold'] << sold
                        datasetsMap['backgroundColors'] << color + "88"
                        datasetsMap['borderColors'] << color
                        labels.add(label)
                    }
                }
                jsonData.labels = labels

                jsonData.datasetsMap = datasetsMap
            }
        }
        log.debug "jsonData: ${jsonData}"

        render jsonData as JSON
    }

    def updateObjective() {
        log.debug "updateObjective: ${params}"
        def ret = [success: false, message: g.message(code: 'sales.objectives.update.error')]

        if (params.value && (params.interest || params.make || params.isUsed) && params.month && params.year && params.departmentId) {
            Department department
            if (params.departmentId) {
                department = Department.get(params.departmentId as long)
            }
            ClientInterest interest
            if (params.interest) {
                interest = ClientInterest.findByName(params.interest)
            }
            Boolean isUsed
            if (params.isUsed) {
                isUsed = params.isUsed == "Used"
            }
            if (interest || params.make || isUsed != null) {
                ObjectiveComparator comparator = ObjectiveComparator.createCriteria().get {
                    if (params.year != '-1' && params.month != '-1') {
                        isNotNull("date")
                        eq("year", params.year as int)
                        eq("month", params.month as int)
                    } else {
                        isNull('date')
                    }
                    interest ? eq("interest", interest) : isNull("interest")
                    params.make ? eq("make", params.make) : isNull("make")
                    isUsed ? eq("isUsed", isUsed) : isNull("isUsed")
                    eq("department", department)
                }
                log.debug "comparator :${comparator}"
                if (comparator) {
                    comparator.value = Integer.parseInt(params.value)
                } else {
                    Calendar c = Calendar.getInstance()
                    c.set(Calendar.MILLISECOND, 0)
                    c.set(Calendar.SECOND, 0)
                    c.set(Calendar.MINUTE, 0)
                    c.set(Calendar.HOUR_OF_DAY, 0)
                    c.set(Calendar.MONTH, (params.month as int) - 1)
                    c.set(Calendar.YEAR, params.year as int)
                    c.set(Calendar.DAY_OF_MONTH, 1)
                    comparator = new ObjectiveComparator(department: department, make: params.make, interest: interest, isUsed: isUsed, date: params.month == '-1' && params.year == '-1' ? null : c.getTime(), value: params.value as int)
                }
                comparator = salesObjectiveReportService.saveComparator(comparator)
                if (comparator) {
                    ret = [success: true, message: g.message(code: 'sales.objectives.update.success')]
                }
            }
        }

        render ret as JSON
    }


}