package traction

import com.google.common.collect.ArrayListMultimap
import com.google.common.collect.ListMultimap
import grails.converters.JSON
import grails.gorm.PagedResultList
import grails.plugin.springsecurity.annotation.Secured
import org.grails.web.json.JSONArray
import org.grails.web.json.JSONObject
import traction.client.ClientInterest
import traction.container.Livestat
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityDashboardSearch
import traction.opportunity.OpportunityLabel
import traction.origin.Origin
import traction.permissions.PermissionLevel
import traction.security.User

@Secured('ROLE_USER')
class ClientDashboardController {

    def opportunityService
    def springSecurityService
    def historyDashboardService
    def potentialOpportunityService
    def historyService
    def userGoalService

    def index() {
        User currentUser = springSecurityService.currentUser
        if (currentUser.permissionLevel == PermissionLevel.TELEPHONY) {
            redirect(controller: "callNotes", action: "index")
        } else {
            render(view: "index")
        }
    }

    def dashboard() {
        JSONObject filter = request.JSON
        User currentUser = springSecurityService.currentUser
        List<User> users = []
        if (filter) {
            JSONArray usersJSON = filter.getJSONArray('userIds')
            usersJSON.each {
                log.debug "id : ${it}"
                User user = User.get(it as long)
                log.debug "user : ${user}"
                if (user) {
                    users.add(user)
                }
            }
        } else {
            users.add(currentUser)
        }

        List<Integer> userIds = []
        for (User user : users) {
            userIds.add(user.id)
        }

        List<OpportunityLabel> labels = OpportunityLabel.getAll()
        List<ClientInterest> interests = ClientInterest.getAll()
        List<Origin> origins = Origin.getAll()
        render(template: "dashboard", model: [
                labels             : labels,
                interests          : interests,
                origins            : origins,
                user               : currentUser,
                users              : users,
                userIds            : userIds
        ])
    }

    def livestats() {
        OpportunityDashboardSearch dashboardSearchCookie = new OpportunityDashboardSearch(request.JSON)

        OpportunityDashboardSearch dashboardSearch = new OpportunityDashboardSearch()
        dashboardSearch.isCount = true
        dashboardSearch.users = dashboardSearchCookie.users

        dashboardSearch.awaitingOnly = true
        int awaitingNotContacted = opportunityService.search(dashboardSearch)
        dashboardSearch.awaitingOnly = false

        dashboardSearch.awaitingContactedOnly = true
        int awaitingContacted = opportunityService.search(dashboardSearch)
        dashboardSearch.awaitingContactedOnly = false

        dashboardSearch.deliveryOnly = true
        int delivery = opportunityService.search(dashboardSearch)
        dashboardSearch.deliveryOnly = false

        dashboardSearch.appointmentOnly = true
        int appointment = opportunityService.search(dashboardSearch)
        dashboardSearch.appointmentOnly = false

        dashboardSearch.taskTodoOnly = true
        int todo = opportunityService.search(dashboardSearch)
        dashboardSearch.taskTodoOnly = false

        dashboardSearch.taskSoonOnly = true
        int soon = opportunityService.search(dashboardSearch)
        dashboardSearch.taskSoonOnly = false

        int sales = 0
        int goal = 0
        dashboardSearch.users.each { user ->
            sales += historyService.getHistorybyFormula([
                    formula: "USEROPPORTUNITYSOLDBYFILTER",
                    filter: "[date:date_lastmonth]",
                    method: "COUNT",
                    user: user
            ])
            goal += userGoalService.getUserSalesTarget(user) ? userGoalService.getUserSalesTarget(user).month : 0
        }

        Livestat livestatWaiting = new Livestat(
                color: "secondary",
                colorCss: "var(--bs-secondary)",
                icon: "phone-slash",
                title1: "dashboard.waiting.contacted",
                tooltip1: "dashboard.waiting.contacted.tooltip",
                onclick1: "setSimpleFilter('awaitingContactedOnly')",
                result1: awaitingContacted,
                title2: "dashboard.waiting.notcontacted",
                tooltip2: "dashboard.waiting.notcontacted.tooltip",
                onclick2: "setSimpleFilter('awaitingOnly')",
                result2: awaitingNotContacted
        )

        Livestat livestatDelivery = new Livestat(
                color: "",
                colorCss: "var(--bs-orange)",
                icon: "calendar-clock",
                title1: "dashboard.appointment",
                tooltip1: "dashboard.appointment.tooltip",
                onclick1: "setSimpleFilter('appointmentOnly')",
                result1: appointment,
                title2: "dashboard.delivery",
                tooltip2: "dashboard.delivery.tooltip",
                onclick2: "setSimpleFilter('deliveryOnly')",
                result2: delivery
        )
        Livestat livestatTasks = new Livestat(
                color: "success",
                colorCss: "var(--bs-success)",
                icon: "list-check",
                title1: "dashboard.tasklate",
                tooltip1: "dashboard.tasklate.tooltip",
                result1: todo,
                onclick1: "setSimpleFilter('taskTodoOnly')",
                title2: "dashboard.tasksoon",
                tooltip2: "dashboard.tasksoon.tooltip",
                result2: soon,
                onclick2: "setSimpleFilter('taskSoonOnly')"
        )
        Map userIdsByGroup = [:]
        Map usersByGroup = dashboardSearch.users.groupBy {it.userGroup?.userProperty }
        usersByGroup.each { key, val ->
            if (key) {
                userIdsByGroup.put(key + "[]", val.id.join(','))
            }
        }
        Map params = ['initDate': 'today', 'status[]': Opportunity.Status.POTENTIAL.id]
        params.putAll(userIdsByGroup)
        Livestat livestatPotential = new Livestat(
                color: "warning",
                colorCss: "var(--bs-warning)",
                icon: "magnifying-glass-dollar",
                title1: "today.potential.opportunity",
                tooltip1: "today.potential.opportunity.tooltip",
                onclick1: "location.href = '" + g.createLink(absolute: true, controller: "leads", action: "index", params: params) + "';",
                result1: potentialOpportunityService.getTodayCount(dashboardSearch.users),
                title2: "sales.objectives",
                tooltip2: "sales.objectives.tooltip",
                result2: sales + " / " + goal
        )
        render(template: "livestats", model: [
                livestatWaiting    : livestatWaiting,
                livestatPotential  : livestatPotential,
                livestatTasks      : livestatTasks,
                livestatDelivery   : livestatDelivery
        ])
    }

    def getDashboardStats() {
        def ret = [lastAct: [], creation: []]

        JSONObject filter = request.JSON
        if (filter) {
            List<User> users = []
            JSONArray usersJSON = filter.getJSONArray('userIds')
            usersJSON.each {
                User user = User.get(it as long)
                users.add(user)
            }

            ListMultimap<Opportunity.Status, Integer> lastAct = ArrayListMultimap.create()
            ListMultimap<Opportunity.Status, Integer> creation = ArrayListMultimap.create()
            List statsArr = historyDashboardService.getDatesByStatus(users)
            statsArr.each {
                lastAct.put(it[0].message, it[2])
                creation.put(it[0].message, it[3])
            }

            statsArr = historyDashboardService.getDatesByCategories(users)
            statsArr.each {
                lastAct.put(Opportunity.Category.ACTIVE.message, it[1])
                creation.put(Opportunity.Category.ACTIVE.message, it[2])
            }

            ret = [lastAct: lastAct.asMap(), creation: creation.asMap()]
        }
        render ret as JSON
    }

    def getOpportunitiesByStatusOrCategory() {
        OpportunityDashboardSearch search = new OpportunityDashboardSearch(request.JSON)
        if (params.status && params.status.isInteger()) {
            search.status = Opportunity.Status.getById(Integer.valueOf(params.status))
        }
        if (params.category) {
            search.category = Opportunity.Category.getById(Integer.valueOf(params.category))
        }
        search.isCount = true
        int dataCount = opportunityService.search(search)
        search.isCount = false
        if (params.max) {
            search.max = params.int("max")
        }
        if (params.offset) {
            search.offset = params.int("offset")
        }
        PagedResultList<Opportunity> data = opportunityService.search(search)
        boolean loadMore = (search.offset + data.size()) < dataCount
        render(template: 'oppsDashboard', model: [opportunities: data, loadMore: loadMore, status: search.status, category: search.category])
    }

    def getOpportunitiesCounters() {
        OpportunityDashboardSearch search = new OpportunityDashboardSearch(request.JSON)
        search.countByStatus = true
        def data = opportunityService.search(search)
        data.each {
            it[0] = it[0].id
            it[1] = it[1].id
        }
        render data as JSON
    }

    def getRatio() {
        OpportunityDashboardSearch search = new OpportunityDashboardSearch(request.JSON)
        def statusData = opportunityService.getStatusSoldRatioMap(search.users, search.from, search.to, request.locale)
        def categoryData = opportunityService.getCategorySoldRatioMap(search.users, search.from, search.to, request.locale)
        def data = [
                status: statusData,
                category: categoryData
        ]
        render data as JSON
    }

}
