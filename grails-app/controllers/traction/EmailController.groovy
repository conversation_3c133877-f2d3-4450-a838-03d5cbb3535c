package traction

import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import traction.communication.CommunicationService
import traction.communication.MailMessage
import traction.communication.MailMessageService

//@Secured('ROLE_ADMIN')
@Secured("@securityService.secured('PERM_EMAIL_ERROR_ADMIN')")
class EmailController {
    MailMessageService mailMessageService
    SpringSecurityService springSecurityService
    CommunicationService communicationService
    EmailParserService emailParserService

    def index() {
        List<MailMessage> errorEmails = mailMessageService.getErrorEmails()

        render(view: 'index', model: [errorEmails: errorEmails])
    }

    def removeEmailError() {
        log.debug "removeEmailError : ${params}"
        def ret = [success: false, message: g.message(code: 'error.occurred')]
        if (params.id) {
            MailMessage mailMessage = MailMessage.get(params.id as long)
            if (mailMessage) {
                mailMessage.parsingError = false
                mailMessage = communicationService.save(mailMessage)
                if (mailMessage) {
                    ret = [success: true, message: g.message(code: 'mail.errors.success')]
                }
            } else {
                ret = [success: false, message: g.message(code: 'mail.errors.error.noId')]
            }
        } else {
            ret = [success: false, message: g.message(code: 'mail.errors.error.noId')]
        }
        render ret as JSON
    }

    @Secured('ROLE_USER')
    def getContent() {
        MailMessage mailMessage = MailMessage.get(params.long("id"))
        if (!mailMessage) {
            return render("")
        }
        if (!mailMessage.userCanAccess(springSecurityService.currentUser)) {
            return render(text: g.message(code:"communication.isPrivate.content", args: [mailMessage.user?.fullName]), contentType: "text/plain", encoding: "UTF-8")
        }
        String content = params.boolean("original") ? mailMessage.getBodyAsHTML() : mailMessage.text
        String style = """
            <style>
                body {
                    font-family: roboto, sans-serif;
                    color: #333333;
                    line-height: 1.6em;
                }
                a {
                    color: var(--bs-primary);
                    text-decoration: none;
                }
                a:hover {
                    text-decoration: underline;
                }
                
            </style>
        """
        render(
                text: "<base target=\"_blank\" />${style}${content}", // Makes all link open in new tab
                contentType: "text/html",
                encoding: "UTF-8"
        )
    }

    @Secured('ROLE_DEV')
    def initParser() {
        emailParserService.init()
    }

}
