package traction.training

import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import org.springframework.context.i18n.LocaleContextHolder
import traction.GormEntityUtils
import traction.Media
import traction.history.History
import traction.opportunity.Opportunity
import traction.opportunity.OpportunityLabel
import traction.category.CategoryFile
import traction.category.CategoryIcon
import traction.file.FileData
import traction.origin.Origin
import traction.vehicle.Vehicle
import traction.workflow.WorkflowMaster

//@Secured(['ROLE_ADMIN','ROLE_USER'])
@Secured("@securityService.secured('PERM_TRAINING_READ')")
class TrainingController {

    def trainingService
    def fileService
    def configService

    def index() {
        log.debug "TraningController.updateSection.." + params
        def parent = null
        def sections
        def docs
        def superParent
        TrainingSection sectionTraction = trainingService.getTractionSuperSection(LocaleContextHolder.getLocale().getLanguage())

        if (params.id && params.id.equals("0") == false) {
            parent = trainingService.getSection(params.id)
            sections = trainingService.getSectionChilds(params.id)

            if (parent) {
                if (parent.parent == 0) {
                    superParent = parent
                }
                else {
                    superParent = parent.superParent
                }
                docs = parent.trainingDocs.sort{ it.ordre }
            }
        }
        else {
            sections = trainingService.getAllSuperParent()

            if (sectionTraction) {
                sections.add(sectionTraction)
            }
        }


        Boolean alterable = superParent ? trainingService.isAlterable(superParent) : true

        String navigationBar = trainingService.getHtmlHeaderLabel(parent)

        render (view: 'index', model: [superParent: superParent, sections: sections, parent: parent, docs: docs, navigationBar: navigationBar, alterable: alterable, sectionTraction: sectionTraction])
    }

    def modaltrainingdoc() {
        TrainingSection parent = TrainingSection.get(params.long("trainingParentId"))
        TrainingDoc doc = TrainingDoc.get(params.long("trainingDocId"))
        int maxOrdre = parent.trainingDocs ? parent.trainingDocs.size() : 0
        String titre
        if (doc) {
            titre = "training.doc.edit.title"
        } else {
            titre = "training.doc.new.title"
            maxOrdre++
        }
        render(template: "modaltrainingdoc", model: [
                titre: titre,
                maxOrdre: maxOrdre,
                doc: doc,
                parent: parent
        ])
    }

    def modaltrainingsection() {
        TrainingSection section = TrainingSection.get(params.long("trainingSectionId"))
        TrainingSection parent = TrainingSection.get(params.long("trainingParentId"))
        TrainingSection superParent = TrainingSection.get(params.long("trainingSuperParentId"))
        int maxOrdre = trainingService.getSectionChilds(parent ? parent.id : 0).size()
        String titre
        if (section) {
            titre = "training.edit.title"
        } else {
            titre = "training.new.title"
            maxOrdre++
        }
        render(template: "modaltrainingsection", model: [
                titre: titre,
                maxOrdre: maxOrdre,
                section: section,
                parent: parent,
                superParent: superParent
        ])
    }

    def file() {
        if (params.id) {
            def file = FileData.get(params.id)
            if (file) {
                render(template: "file", model: [file: file, hidePrev: true])
            }
        }
        render ""
    }

    def doc() {
        log.debug "TraningController.doc.."+params
        def doc
        if (params.id) {
            doc = trainingService.getDoc(params.id)
        }
        if (!doc) {
            return redirect(action: "index")
        }
        else {
            String navigationBar = trainingService.getHtmlHeaderLabel(doc.trainingSection)
            Boolean alterable = trainingService.isAlterable(doc)
            render (view: 'document', model: [doc: doc, navigationBar: navigationBar, alterable: alterable])
        }
    }

    def updateDoc() {
        log.debug "TraningController.oc.."+params
        def doc
        if (params.id) {
            doc = trainingService.getDoc(params.id)
        }
        if (!doc) {
            render "Document not existing"
        }
        else {
            String navigationBar = trainingService.getHtmlHeaderLabel(doc.trainingSection)
            Boolean alterable = trainingService.isAlterable(doc)
            render (template: 'doc', model: [doc: doc, navigationBar: navigationBar, alterable: alterable])
        }
    }

    def getDocsTreeJSON() {
        log.debug "getDocsTreeJSON${params}"
        def ret = []
        def map = [:]
        def superParent
        if (params.superParentId.equals("") == false) {
            superParent = trainingService.getSection(params.superParentId)
        }
        render trainingService.getDocsTreeJSON(superParent) as JSON
    }

    def getSectionsTreeJSON() {
        log.debug "getSectionsTreeJSON${params}"
        def ret = []
        def map = [:]
        def superParent
        Long currentSectionId = 0
        if (params.superParentId.equals("") == false) {
            superParent = trainingService.getSection(params.superParentId)
        }
        if (params.sectionId.equals("") == false) {
            currentSectionId = Long.valueOf(params.sectionId)
        }
        render trainingService.getSectionsTreeJSON(superParent, currentSectionId) as JSON
    }

    //@Secured(['ROLE_TRAINING_WRITE'])
    @Secured("@securityService.secured('PERM_TRAINING_WRITE')")
    def editSection() {
        log.debug "TraningController.editSection......"+params
        def ret = [success: false, message: "training.section.error.notsaved"]

        def section = new TrainingSection()
        if (params.sectionId) {
            section = trainingService.getSection(params.sectionId as Long)
        }

        if (params.superParentId && params.superParentId.isInteger()) {
            if (!section.superParent) {
                section.superParent = trainingService.getSection(params.superParentId as Long)
            }
        }
        section.title = params.title
        section.description = params.description
        section.ordre = params.modalSectionOrdre.toInteger()
        ret = trainingService.saveSection(section)

        if (ret.success) {
            def f = params.image
            if (!f.isEmpty()) {
                if (section.image) {
                    FileData toDel = section.image
                    section.image = null
                    fileService.delete(toDel)
                }
                def addFileParams = [
                        filecontent: f,
                        category   : CategoryFile.TRAINING.message
                ]
                def file = fileService.addFile(addFileParams)

                if (file?.id) {
                    section.image = file
                }
                ret = trainingService.saveSection(section)
            }
            def newParentId = params.parentId.toInteger()
            def newParent = trainingService.getSection(newParentId)

            if (newParentId == 0 || newParent) {
                def oldParentId = section.parent

                if (oldParentId != newParentId) {

                    if (trainingService.isInChilds(newParent, section.id)) {
                        return render([success: false, message: g.message(code: "training.section.error.movetochild", args: [section.title ])] as JSON)
                    }
                    section.parent = newParentId
                    ret = trainingService.saveSection(section)
                    trainingService.orderSet(trainingService.getSectionChilds(oldParentId))
                }
                def changes = trainingService.getSectionChilds(section.parent)
                changes.removeElement(section)
                int o = 1

                TrainingSection.withTransaction {
                    changes.each {
                        if(o == section.ordre){
                            o++;
                        }
                        if(it.ordre != o){
                            it.ordre = o
                            it.save(flush:true)
                        }
                        o++;
                    }
                }
            }
            else {
                ret = [success: false, message: 'training.section.error.parent']
            }
        }
        ret.message = g.message(code: ret.message)
        render(ret as JSON)
    }

    //@Secured(['ROLE_TRAINING_WRITE'])
    @Secured("@securityService.secured('PERM_TRAINING_WRITE')")
    def deleteSection() {
        def section
        def ret = [success: false, message: 'Error.']
        if (params.sectionId) {
            section = trainingService.getSection(params.sectionId)
            ret = trainingService.deleteSection(section)
            ret.message = message(code: ret.message)
        }
        render ret as JSON
    }

    //@Secured(['ROLE_TRAINING_WRITE'])
    @Secured("@securityService.secured('PERM_TRAINING_WRITE')")
    def deleteDoc() {
        def doc
        def ret = [success: false, message: 'Error.']
        if (params.docId) {
            doc = trainingService.getDoc(params.docId)
            ret = trainingService.deleteDoc(doc)
            ret.message = message(code: ret.message)
        }
        render ret as JSON

    }

    //@Secured('ROLE_TRAINING_WRITE')
    @Secured("@securityService.secured('PERM_TRAINING_WRITE')")
    def editDoc() {
        log.debug "TraningController.editDoc.."+params
        def doc = new TrainingDoc()
        def oldParent
        if (params.docId) {
            doc = trainingService.getDoc(params.docId)
            oldParent = doc.trainingSection
        }
        def section
        if (params.parentId) {
            section = trainingService.getSection(params.parentId)
        }
        if (oldParent && oldParent != section) {
            oldParent.removeFromTrainingDocs(doc)
            doc.trainingSection = section
            trainingService.saveSection(oldParent)
        }
        else {
            doc.trainingSection = section
        }
        doc.title = params.title
        doc.description = params.description
        doc.ordre = params.modalDocOrdre.toInteger()
        def ret = trainingService.saveDoc(doc)

        ret.message = g.message(code: ret.message)

        if (ret.success) {
            ret.id = doc.id
            def toOrder = section.trainingDocs.sort{ it.ordre }
            toOrder.removeElement(doc)
            int o = 1
            TrainingDoc.withTransaction {
                toOrder.each {
                    if (o == doc.ordre) {
                        o++
                    }
                    if (it.ordre != o) {
                        it.ordre = o
                        GormEntityUtils.save(it)
                    }
                    o++
                }
            }
            if (oldParent && oldParent != section) {
                trainingService.orderSet(oldParent.trainingDocs)
            }
        }

        render(ret as JSON)
        return
    }

    def fixFilesOrder() {
        log.debug "fixFilesOrder"
        if (params.docId) {
            def doc = trainingService.getDoc(params.docId)
            if (doc.files) {
                trainingService.orderSet(doc.files)
            }
        }
        render "DONE"
    }

    def system() {
        List<Map> workflows = WorkflowMaster.getAll().collect {
            [
                    workflow : it,
                    cardTypes: configService.getCardTypes(it.id).collect { type ->
                        [
                                type       : type,
                                opportunity: trainingService.getDemoOpportunity(type.id, null, null)
                        ]
                    }
            ]
        }

        def pendingColors = configService.getAllPendingColor()

        List<Map> icons = CategoryIcon.getAll().message.collect {
            [
                    message: it,
                    icons  : configService.getIcons(it).collect { icon ->
                        [
                                icon       : icon,
                                opportunity: trainingService.getDemoOpportunity(0, icon, null)
                        ]
                    }
            ]
        }
        List<Map> labels = OpportunityLabel.getAll().collect {
            [
                    label: it,
                    opportunity: trainingService.getDemoOpportunity(0, null, it)
            ]
        }

        List<Map> calendarColors = []
        Opportunity noWorkflow = trainingService.getDemoOpportunity(0, null, null)
        noWorkflow.workflowData = null
        calendarColors.add([
                message    : "card.calendar.noworkflow.explanation",
                opportunity: noWorkflow
        ])

        Opportunity red = trainingService.getDemoOpportunity(0, null, null)
        red.workflowData.dateStart = new Date() - 10
        red.workflowData.dateParts = new Date() - 7
        red.workflowData.dateEnd = new Date() - 1
        calendarColors.add([
                message    : "card.calendar.red.explanation",
                opportunity: red
        ])

        Opportunity purple = trainingService.getDemoOpportunity(0, null, null)
        purple.workflowData.dateStart = new Date()
        purple.workflowData.dateParts = new Date()
        purple.workflowData.dateEnd = new Date()
        calendarColors.add([
                message    : "card.calendar.purple.explanation",
                opportunity: purple
        ])

        Opportunity yellow = trainingService.getDemoOpportunity(0, null, null)
        yellow.workflowData.dateStart = new Date() + 1
        yellow.workflowData.dateParts = new Date() + 1
        yellow.workflowData.dateEnd = new Date() + 1
        calendarColors.add([
                message    : "card.calendar.yellow.explanation",
                opportunity: yellow
        ])

        Opportunity white = trainingService.getDemoOpportunity(0, null, null)
        white.workflowData.dateStart = new Date() + 2
        white.workflowData.dateParts = new Date() + 4
        white.workflowData.dateEnd = new Date() + 6
        calendarColors.add([
                message    : "card.calendar.white.explanation",
                opportunity: white
        ])
        List<Map> historyScores = []
        History.Status.values().each { status ->
            historyScores.add([
                    status: status,
                    points: configService.getHistoryScore(status)
            ])
        }
        historyScores = historyScores.sort { it.points }.reverse()
        List<Media> medias = Media.values()
        List<Origin> origins = Origin.getAll()

        def vehicleStatus = Vehicle.Status.collect()

        render(view: "system", model: [origins: origins, medias: medias, historyScores: historyScores, workflows: workflows, pendingColors: pendingColors, icons: icons, calendarColors: calendarColors, labels: labels, vehicleStatus: vehicleStatus])
    }
}
